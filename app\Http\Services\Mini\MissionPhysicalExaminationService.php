<?php

namespace App\Http\Services\Mini;

use App\Exceptions\ErrorTrait;
use App\Http\Utils\ScopeQueryImpl;
use App\Models\MissionPhysicalExamination;
use App\Models\MissionPerson;
use App\Models\RegionManager;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MissionPhysicalExaminationService
{
    use ErrorTrait;

    /**
     * 获取体检计划列表
     * 
     * @param array $params
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function list(array $params)
    {
        $user = auth()->user();
        
        // 根据用户权限过滤数据
        $builder = MissionPhysicalExamination::query()
            ->with(['mission' => function ($query) {
                $query->select('id', 'name', 'year');
            }]);

        // 支持按任务ID筛选
        if (!empty($params['mission_id'])) {
            $builder->where('mission_id', $params['mission_id']);
        }

        // 支持按状态筛选
        if (isset($params['status'])) {
            $builder->where('status', $params['status']);
        }

        // 支持按时间范围筛选
        if (!empty($params['start_time'])) {
            $builder->where('time', '>=', $params['start_time']);
        }
        if (!empty($params['end_time'])) {
            $builder->where('time', '<=', $params['end_time']);
        }

        // 支持按医院区域筛选
        if (!empty($params['hospital_region_code'])) {
            $builder->where('hospital_region_code', $params['hospital_region_code']);
        }

        $builder->orderBy('time', 'desc');
        
        return $builder->paginate(pageSize());
    }

    /**
     * 获取体检人员列表
     * 
     * @param array $params
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function personList(array $params)
    {
        $user = auth()->user();
        
        // 根据用户权限过滤数据
        $builder = MissionPerson::query()
            ->whereNotNull('mission_physical_examination_id')
            ->with([
                'mission' => function ($query) {
                    $query->select('id', 'name', 'year');
                },
                'missionPhysicalExamination' => function ($query) {
                    $query->select('id', 'time', 'address', 'hospital_region_name');
                }
            ]);

        // 支持按任务ID筛选
        if (!empty($params['mission_id'])) {
            $builder->where('mission_id', $params['mission_id']);
        }

        // 支持按体检计划ID筛选
        if (!empty($params['mission_physical_examination_id'])) {
            $builder->where('mission_physical_examination_id', $params['mission_physical_examination_id']);
        }

        // 支持按体检结果筛选
        if (isset($params['physical_check'])) {
            $builder->where('physical_check', $params['physical_check']);
        }

        // 支持按复查结果筛选
        if (isset($params['physical_recheck'])) {
            $builder->where('physical_recheck', $params['physical_recheck']);
        }

        // 支持按抽查结果筛选
        if (isset($params['physical_spot_check'])) {
            $builder->where('physical_spot_check', $params['physical_spot_check']);
        }

        // 支持按姓名搜索
        if (!empty($params['name'])) {
            $builder->where('name', 'like', '%' . $params['name'] . '%');
        }

        // 支持按身份证号搜索
        if (!empty($params['id_card'])) {
            $builder->where('id_card', 'like', '%' . $params['id_card'] . '%');
        }

        $builder->orderBy('id', 'desc');
        
        return $builder->paginate(pageSize());
    }
} 