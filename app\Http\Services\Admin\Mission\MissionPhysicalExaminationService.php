<?php

namespace App\Http\Services\Admin\Mission;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\MissionConst;
use App\Http\Consts\MissionPersonConst;
use App\Http\Consts\RegionConst;
use App\Http\Utils\ScopeQueryImpl;
use App\Models\MissionPhysicalExamination;
use App\Models\MissionRegionSetting;
use App\Models\Region;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Exception;

class MissionPhysicalExaminationService
{
    use ErrorTrait;

    public function stationRateStore(mixed $params)
    {
        $va = Validator::make($params, [
            'mission_id'    => 'required',
            'station_rate'  => 'required',
            'district_code' => 'required',
        ], [
            'mission_id.required'    => '请选择任务',
            'station_rate.required'  => '请填写上站率',
            'district_code.required' => '请填写区县',
        ]);
        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }
        $districtCode = $params['district_code'];
        $missionId    = $params['mission_id'];

        $user     = auth()->user();
        $district = $user->managerRegion()->where("regions.level", RegionConst::levelDistrict)->where('code', $districtCode)->first();
        if (!$district) {
            $this->setError("没有管理的区县");
            return false;
        }

        DB::beginTransaction();
        try {
            $regionSetting = MissionRegionSetting::query()->where('mission_id', $missionId)->where('code', $districtCode)->first();
            if (!$regionSetting) {
                $this->setError("请先设置区县任务数");
                return false;
            }
            $regionSetting->update([
                'physical_examination_station_rate' => $params['station_rate'],
            ]);
            MissionRegionSetting::query()
                ->where('mission_id', $missionId)
                ->where('tree_code', 'like', $district->tree_code . "%")->update([
                    'physical_examination_station_rate' => $params['station_rate'],
                ]);
            // 更新任务数
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            $this->setError($exception->getMessage());
            return false;
        }
    }

    public function stationRateShow(mixed $params)
    {
        $va = Validator::make($params, [
            'mission_id' => 'required',
        ], [
            'mission_id.required' => '请选择任务',
        ]);
        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }

        $missionId = $params['mission_id'];

        $user     = auth()->user();
        $district = $user->managerRegion()->where("regions.level", RegionConst::levelDistrict)->first();
        if (!$district) {
            $this->setError("没有管理的区县");
            return false;
        }

        $regionSetting = MissionRegionSetting::query()->where('mission_id', $missionId)->where('code', $district->code)->first();
        if (!$regionSetting) {
            $this->setError("请先设置区县任务数");
            return false;
        }
        return $regionSetting;
    }

    public function index($params)
    {
        $user    = auth()->user();
        $builder = ScopeQueryImpl::adminGetMissionPhysicalExaminationQuery($user, $params);
        $builder->orderBy("id", "desc");
        $items = $builder->paginate(pageSize());
        return $items;
    }

    public function store($params)
    {
        $va = Validator::make($params, [
            'mission_id'           => 'required',
            'time'                 => 'required',
            'contact'              => 'required',
            'contact_phone'        => 'required',
            'hospital_region_code' => 'required',
            'address'              => 'required',
            'manager'              => 'required',
            'status'               => 'required',
        ], [
            'mission_id.required'           => '请选择任务',
            'time.required'                 => '请选择时间',
            'contact.required'              => '请填写联系人',
            'contact_phone.required'        => '请填写联系电话',
            'hospital_region_code.required' => '请填写医院地址',
            'address.required'              => '请填写地址',
            'manager.required'              => '请填写负责人',
            'status.required'               => '请填写状态',
        ]);
        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }
        $missionId          = $params['mission_id'];
        $time               = $params['time'];
        $contact            = $params['contact'];
        $contactPhone       = $params['contact_phone'];
        $hospitalRegionCode = $params['hospital_region_code'];
        $address            = $params['address'];
        $manager            = $params['manager'];
        $status             = $params['status'];

        $hospital = Region::query()->where('code', $hospitalRegionCode)->first();

        $user     = auth()->user();
        $district = $user->managerRegion()->where("regions.level", RegionConst::levelDistrict)->first();

        if (!$district) {
            $this->setError("没有管理的区县");
            return false;
        }

        $data = [
            'mission_id'                => $missionId,
            "district_code"             => $district->code,
            "district_name"             => $district->name,
            'time'                      => $time,
            'contact'                   => $contact,
            'contact_phone'             => $contactPhone,
            'hospital_region_code'      => $hospitalRegionCode,
            'hospital_region_name'      => $hospital->name,
            'hospital_region_tree_code' => $hospital->tree_code,
            'address'                   => $address,
            'manager'                   => $manager,
            'status'                    => $status,
        ];


        DB::beginTransaction();
        try {
            $item = MissionPhysicalExamination::query()->create($data);
            DB::commit();
            return $item;
        } catch (Exception $exception) {
            DB::rollBack();
            $this->setError($exception->getMessage());
            return false;
        }
    }

    public function show($id)
    {
        $builder = MissionPhysicalExamination::query();
        $items   = $builder->find($id);
        return $items;
    }

    public function update($params, $id)
    {
        $va = Validator::make($params, [
            'time'                 => 'required',
            'contact'              => 'required',
            'contact_phone'        => 'required',
            'hospital_region_code' => 'required',
            'address'              => 'required',
            'manager'              => 'required',
            'status'               => 'required',
        ], [
            'time.required'                 => '请选择时间',
            'contact.required'              => '请填写联系人',
            'contact_phone.required'        => '请填写联系电话',
            'hospital_region_code.required' => '请填写医院地址',
            'address.required'              => '请填写地址',
            'manager.required'              => '请填写负责人',
            'status.required'               => '请填写状态',
        ]);

        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }

        $missionId          = $params['mission_id'];
        $time               = $params['time'];
        $contact            = $params['contact'];
        $contactPhone       = $params['contact_phone'];
        $hospitalRegionCode = $params['hospital_region_code'];
        $address            = $params['address'];
        $manager            = $params['manager'];
        $status             = $params['status'];

        $hospital = Region::query()->where('code', $hospitalRegionCode)->first();


        $data = [
            'mission_id'                => $missionId,
            'time'                      => $time,
            'contact'                   => $contact,
            'contact_phone'             => $contactPhone,
            'hospital_region_code'      => $hospitalRegionCode,
            'hospital_region_name'      => $hospital->name,
            'hospital_region_tree_code' => $hospital->tree_code,
            'address'                   => $address,
            'manager'                   => $manager,
            'status'                    => $status,
        ];

        DB::beginTransaction();
        try {
            $item = MissionPhysicalExamination::query()->where('id', $id)->first();
            if ($item) {
                $item->update($data);
            }
            DB::commit();
            return $item;
        } catch (Exception $exception) {
            DB::rollBack();
            $this->setError($exception->getMessage());
            return false;
        }
    }

    public function destroy($id, $params)
    {
        if ($id == "batch") {
            $ids = $params['ids'];
            if (is_array($ids)) {
                MissionPhysicalExamination::query()->whereIn('id', $ids)->delete();
            }
        } else {
            MissionPhysicalExamination::query()->where('id', $id)->delete();
        }
        return true;
    }

    public function personList(array $params)
    {
        $user    = auth()->user();
        $builder = ScopeQueryImpl::adminGetMissionPersonQuery($user, $params);
        $builder->orderBy("id", "desc");
        $items = $builder->paginate(pageSize());
        return $items;
    }

    public function personDelete(array $params)
    {
        $ids     = $params['ids'];
        $user    = auth()->user();
        $builder = ScopeQueryImpl::adminGetMissionPersonQuery($user, $params);

        $builder->whereIn('id', $ids)->update([
            'mission_persons.mission_physical_examination_id' => null,
            'mission_persons.physical_check'                  => -1,
        ]);

        return false;
    }

    public function submit(array $params)
    {
        $user    = auth()->user();
        $builder = ScopeQueryImpl::adminGetMissionPhysicalExaminationQuery($user, $params);
        $builder->where('mission_physical_examinations.id', $params['id'])->update([
            'status' => MissionConst::missionPhysicalExaminationStatusSubmitted,
        ]);
        return false;
    }

    public function recall(array $params)
    {
        $user    = auth()->user();
        $builder = ScopeQueryImpl::adminGetMissionPhysicalExaminationQuery($user, $params);
        $builder->where('mission_physical_examinations.id', $params['id'])->update([
            'status' => MissionConst::missionPhysicalExaminationStatusDraft,
        ]);
        return false;
    }

    public function markResult(array $params)
    {
        $va = Validator::make($params, [
            'person_id'      => 'required',
            'physical_check' => 'required',
        ], [
            'person_id.required'      => '请选择人员',
            'physical_check.required' => '请填写检查结果',
        ]);
        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }
        $user          = auth()->user();
        $personId      = $params['person_id'];
        $builder       = ScopeQueryImpl::adminGetMissionPersonQuery($user, []);
        $missionPerson = $builder->where('mission_persons.id', $personId)->first();
        if ($missionPerson) {
            $missionPerson->update([
                'physical_check'        => $params['physical_check'],
                'physical_check_remark' => $params['physical_check_remark'] ?? null,
                'physical_check_at'     => $params['physical_check_at'] ?? null,
                'physical_result'       => $params['physical_check'] == MissionPersonConst::personPhysicalCheckYes ? MissionPersonConst::personPhysicalResultYes : MissionPersonConst::personPhysicalResultNo,
            ]);
        } else {
            $this->setError("设置检查结果失败：人员不存在！");
            return false;
        }
        return false;
    }

    public function markRecheckResult(array $params)
    {
        $va = Validator::make($params, [
            'person_id'        => 'required',
            'physical_recheck' => 'required',
        ], [
            'person_id.required'        => '请选择人员',
            'physical_recheck.required' => '请选择复查结果',
        ]);
        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }
        $user     = auth()->user();
        $personId = $params['person_id'];

        $builder = ScopeQueryImpl::adminGetMissionPersonQuery($user, []);

        $missionPerson = $builder->where('mission_persons.id', $personId)->first();
        if ($missionPerson) {
            $missionPerson->update([
                'physical_recheck'        => $params['physical_recheck'],
                'physical_recheck_remark' => $params['physical_recheck_remark'] ?? null,
                'physical_recheck_at'     => now(),
                'physical_result'         => $params['physical_recheck'] == MissionPersonConst::personPhysicalRecheckYes ? MissionPersonConst::personPhysicalResultYes : MissionPersonConst::personPhysicalResultNo,
            ]);
        } else {
            $this->setError("设置检查结果失败：人员不存在！");
        }
        return true;
    }

    public function markSpotCheckResult(array $params)
    {
        $va = Validator::make($params, [
            'person_id'           => 'required',
            'physical_spot_check' => 'required',
        ], [
            'person_id.required'           => '请选择人员',
            'physical_spot_check.required' => '请选择是否通过',
        ]);
        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }
        $user          = auth()->user();
        $personId      = $params['person_id'];
        $builder       = ScopeQueryImpl::adminGetMissionPersonQuery($user, []);
        $missionPerson = $builder->where('mission_persons.id', $personId)->first();
        if ($missionPerson) {
            $missionPerson->update([
                'physical_spot_check'        => $params['physical_spot_check'],
                'physical_spot_check_remark' => $params['physical_spot_check_remark'] ?? null,
                'physical_result'            => $params['physical_spot_check'] == MissionPersonConst::personPhysicalSpotCheckYes ? MissionPersonConst::personPhysicalResultYes : MissionPersonConst::personPhysicalResultNo,
            ]);
        } else {
            $this->setError("设置检查结果失败：人员不存在！");
        }
        return true;
    }

}
