<?php

use App\Http\Controllers;
use App\Http\Controllers\Admin;
use App\Http\Controllers\Common;
use App\Http\Controllers\Mini;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/


Route::prefix("admin")->group(function () {
    Route::post("login", [Admin\AuthController::class, "login"]);
    Route::post('get_captcha', [Common\CaptchaController::class, 'getCaptcha']);

    Route::middleware(['permissions'])->group(function () {

        Route::middleware(['auth:api'])->group(function () {

            // 上传文件
            Route::post('upload_file', [Common\FileController::class, 'uploadFile']);
            // 获取文件
            Route::post('get_file', [Common\FileController::class, 'getFile']);

            Route::get("navs", [Admin\AuthController::class, "navs"]);

            Route::get("info", [Admin\AuthController::class, "info"]);
            Route::post('update_password', [Admin\AuthController::class, "updatePassword"]);
            Route::resource("article", Admin\ArticleController::class);
            Route::resource("consult_knowledge", Admin\ArticleController::class);
            Route::resource("consult_file", Admin\ConsultFileController::class);

            Route::resource("category", Admin\CategoryController::class);
            Route::resource("business-category", Admin\CategoryController::class);


            Route::resource("menu", Admin\MenuController::class);
            Route::resource("page", Admin\PageController::class);
            Route::resource("role", Admin\RoleController::class);
            Route::resource("permission", Admin\PermissionController::class);
            Route::get("permission-tree", [Admin\PermissionController::class, "getPermissionTree"]);

            Route::resource("user", Admin\UserController::class);
            Route::post("user/{id}/password_reset", [Admin\UserController::class, 'passwordReset']);

            Route::get("user_point", [Admin\UserController::class, "point"]);
            Route::resource("region", Admin\RegionController::class);
            Route::get("region_tree", [Admin\RegionController::class, "tree"]);

            Route::resource("folder", Admin\FolderController::class);
            Route::get("files", [Admin\FolderController::class, "files"]);
            Route::delete('file', [Common\FileController::class, 'delete']);

            Route::get("config", [Admin\ConfigController::class, "get"]);
            Route::post("config", [Admin\ConfigController::class, "update"]);

            /**
             * 获取华为临时token
             */
            Route::get("huawei_security_token", [Controllers\HuaweiController::class, "getSecurityToken"]);
            Route::get("alibaba_security_token", [Controllers\AlibabaController::class, "getSecurityToken"]);

            Route::post("add_file", [Common\FileController::class, "addFile"]);


            Route::resource("stage", Admin\StageController::class);

            /**
             * 课程
             */
            Route::resource("course_type", Admin\CourseTypeController::class);
            Route::resource("course", Admin\CourseController::class);
            Route::resource("chapter", Admin\ChapterController::class);

            /**
             * 题库管理
             */
            Route::resource("knowledge", Admin\KnowledgeController::class);
            Route::resource("question", Admin\QuestionController::class);

            Route::resource("paper", Admin\PaperController::class);
            Route::resource("paper_question", Admin\PaperQuestionController::class);
            Route::get('paper_question_source', [Admin\PaperQuestionController::class, 'source']);

            /**
             * 任务
             */
            Route::resource('task', Admin\Task\TaskController::class)->only([
                'index',
                'store',
            ]);
            Route::resource('import_item', Admin\Task\ImportItemController::class);
            Route::post("task_confirm", [Admin\Task\TaskController::class, 'confirm']);

            /**
             * 授权码
             */
            Route::resource('invitation_code', Admin\InvitationCodeController::class)->only([
                'index',
                'destroy',
                'update'
            ]);

            Route::post('invitation_code_remove_all', [Admin\InvitationCodeController::class, 'removeAll']);

            /**
             * 日常办公
             */
            Route::resource('work_task', Admin\Work\WorkTaskController::class);
            Route::resource('work_task_type', Admin\CategoryController::class);
            Route::resource('work_task_report', Admin\Work\WorkTaskReportController::class);
            Route::resource('inform', Admin\InformController::class);

            /**
             * 举报
             */
            Route::resource('report', Admin\ReportController::class);
            Route::post('report/{id}/reply', [Admin\ReportController::class, 'reply']);

            /**
             * 调查问卷
             */
            Route::resource('survey', Admin\Work\SurveyController::class);
            Route::resource('survey_form', Admin\Work\SurveyFormController::class)->only([
                'index',
                'show',
                'destroy',
            ]);

            Route::get('survey/{id}/statistics', [Admin\Work\SurveyController::class, 'statistics']);
            Route::post('survey/{id}/qrcode', [Admin\Work\SurveyController::class, 'qrcode']);

            /**
             * 智能通知回访
             */
            Route::resource("out_notice_task", Admin\OutNotice\OutNoticeTaskController::class);
            Route::post("out_notice_task/{id}/start", [Admin\OutNotice\OutNoticeTaskController::class, "start"]);
            Route::post("out_notice_task/{id}/stop", [Admin\OutNotice\OutNoticeTaskController::class, "stop"]);

            Route::resource("out_notice_task_receive", Admin\OutNotice\OutNoticeTaskReceiveController::class);
            Route::post("out_notice_task_receive_run", [Admin\OutNotice\OutNoticeTaskReceiveController::class, "run"]);
            Route::post("out_notice_task_receive_clear", [Admin\OutNotice\OutNoticeTaskReceiveController::class, "clear"]);

            Route::get('out_notice_task/{id}/robot_statistics', [Admin\OutNotice\OutNoticeTaskReceiveController::class, "robotStatistics"]);


            Route::resource("sms_template", Admin\OutNotice\SmsTemplateController::class);
            Route::resource("vms_voice_template", Admin\OutNotice\VmsVoiceTemplateController::class);
            Route::resource("ccs_robot_template", Admin\OutNotice\CcsRobotTemplateController::class);
            Route::post("sms_template/{id}/refresh", [Admin\OutNotice\SmsTemplateController::class, "refresh"]);
            Route::get("get_sms_upload_signature", [Admin\OutNotice\SmsTemplateController::class, "getSmsUploadSignature"]);


            /**
             * 特殊时期征召
             */
            Route::resource('recall_task', Admin\Recall\RecallTaskController::class);
            Route::resource('recall_task_soldier', Admin\Recall\RecallTaskSoldierController::class);
            Route::post('recall_task_soldier_clear', [Admin\Recall\RecallTaskSoldierController::class, "clear"]);
            Route::get('recall_task_soldier_search_option', [Admin\Recall\RecallTaskSoldierController::class, "searchOption"]);

            // 征兵任务
            Route::resource("mission", Admin\Mission\MissionController::class);
            Route::resource("mission_person", Admin\Mission\MissionPersonController::class);
            Route::resource("mission_task_num", Admin\Mission\MissionTaskNumController::class);

            Route::group(['prefix' => 'statistics'], function () {
                // 人员相关统计
                Route::get('mission_school_person_list', [Admin\StatisticsController::class, "missionSchoolPersonList"]);
                Route::get('mission_school_person_chart', [Admin\StatisticsController::class, "missionSchoolPersonChart"]);
                Route::get('mission_social_person_list', [Admin\StatisticsController::class, "missionSocialPersonList"]);
                Route::get('mission_social_person_chart', [Admin\StatisticsController::class, "missionSocialPersonChart"]);

                // 任务相关统计
                Route::get('mission_promote_compare_list', [Admin\StatisticsController::class, "missionPromoteCompareList"]);
                Route::get('mission_intention_list', [Admin\StatisticsController::class, "missionIntentionList"]);
                Route::get('mission_political_exam_list', [Admin\StatisticsController::class, "missionPoliticalExamList"]);
                Route::get('mission_education_list', [Admin\StatisticsController::class, "missionEducationList"]);

                Route::get('mission_physical_task_num_list', [Admin\StatisticsController::class, "missionPhysicalTaskNumList"]);
                Route::get('mission_physical_check_list', [Admin\StatisticsController::class, "missionPhysicalCheckList"]);
                Route::get('mission_physical_recheck_list', [Admin\StatisticsController::class, "missionPhysicalRecheckList"]);
                Route::get('mission_physical_spot_check_list', [Admin\StatisticsController::class, "missionPhysicalSpotCheckList"]);
                Route::get('mission_pre_store_list', [Admin\StatisticsController::class, "missionPreStoreList"]);
                Route::get('mission_task_num_list', [Admin\StatisticsController::class, "missionTaskNumList"]);
                Route::get('mission_summary_list', [Admin\StatisticsController::class, "missionSummaryList"]);
                Route::get('mission_detail', [Admin\StatisticsController::class, "missionDetail"]);
                Route::get('mission_go_list', [Admin\StatisticsController::class, "missionGoList"]);
            });

            // 标记意向
            Route::post('mission_person_mark_intention', [Admin\Mission\MissionPersonController::class, "markIntention"]);

            // 宣传比对
            Route::resource("mission_sign_person", Admin\Mission\MissionSignPersonController::class);
            Route::post("mission_sign_person_append", [Admin\Mission\MissionSignPersonController::class, 'append']);
            Route::get("mission_promote_compare", [Admin\Mission\MissionPersonController::class, 'promoteCompareList']);

            // 体格检查
            Route::post("mission_physical_examination_station_rate", [Admin\Mission\MissionPhysicalExaminationController::class, 'stationRateStore']);
            Route::get("mission_physical_examination_station_rate", [Admin\Mission\MissionPhysicalExaminationController::class, 'stationRateShow']);

            Route::resource("mission_physical_examination", Admin\Mission\MissionPhysicalExaminationController::class);
            Route::post("mission_physical_examination_recall", [Admin\Mission\MissionPhysicalExaminationController::class, 'recall']);
            Route::post("mission_physical_examination_submit", [Admin\Mission\MissionPhysicalExaminationController::class, 'submit']);
            Route::get("mission_physical_examination_person_list", [Admin\Mission\MissionPhysicalExaminationController::class, 'personList']);
            Route::delete("mission_physical_examination_person_delete", [Admin\Mission\MissionPhysicalExaminationController::class, 'personDelete']);
            Route::post("mission_physical_examination_mark_result", [Admin\Mission\MissionPhysicalExaminationController::class, 'markResult']);
            Route::post("mission_physical_examination_mark_recheck_result", [Admin\Mission\MissionPhysicalExaminationController::class, 'markRecheckResult']);
            Route::post("mission_physical_examination_mark_spot_check_result", [Admin\Mission\MissionPhysicalExaminationController::class, 'markSpotCheckResult']);

            // 政考名单
            Route::get("mission_political_exam_person", [Admin\Mission\MissionPersonController::class, 'politicalExamPerson']);

            // 预储名单
            Route::get("mission_pre_store_person", [Admin\Mission\MissionPersonController::class, 'preStorePerson']);

            // 役前教育
            Route::get("mission_education_person", [Admin\Mission\MissionPersonController::class, 'educationPerson']);

            /**
             * 统计
             */
            Route::get("statistics_course", [Admin\StatisticsController::class, "courseStatistics"]);
            Route::get("statistics_user_course", [Admin\StatisticsController::class, "userCourseStatistics"]);
            Route::get('statistics_department_course', [Admin\StatisticsController::class, "departmentCourseStatistics"]);

            Route::get('statistics_user_paper_round', [Admin\StatisticsController::class, "userPaperRoundStatistics"]);
            Route::get('statistics_department_paper_round', [Admin\StatisticsController::class, "departmentPaperRoundStatistics"]);

            Route::get('statistics_user_point', [Admin\StatisticsController::class, "userPointStatistics"]);

            Route::get('statistics_user_visit', [Admin\StatisticsController::class, "userVisitStatistics"]);
            Route::get('statistics_data_count', [Admin\StatisticsController::class, "dataCountStatistics"]);

            Route::get('statistics_user_call', [Admin\StatisticsController::class, "userCallStatistics"]);


            Route::get('statistics_leader_big_data', [Admin\StatisticsController::class, "bigScreenLeaderStatistics"]);
            Route::get('admin_statistics_leader_big_data_district', [Admin\StatisticsController::class, "bigScreenLeaderDistrictStatistics"]);


            // 通知
            Route::get("notification", [Common\NotificationController::class, 'notification']);
            Route::get("notification/{id}", [Common\NotificationController::class, 'notificationDetail']);
            Route::post("notification_read/{id}", [Common\NotificationController::class, 'notificationRead']);
            /**
             * 其他
             */
            Route::resource('operation_log', Admin\OperationLogController::class)->only([
                'index', 'show',
            ]);
        });
    });
});

Route::prefix('callback')->group(function () {
    Route::any('vms_call_finished', [Admin\OutNotice\OutNoticeTaskCallbackController::class, "vmsCallFinishedCallback"]);
    Route::any('vms_call_voice_finished', [Admin\OutNotice\OutNoticeTaskCallbackController::class, "vmsCallVoiceFinishedCallback"]);
    Route::any('ccs_call_finished', [Admin\OutNotice\OutNoticeTaskCallbackController::class, "ccsCallFinishedCallback"]);
});


Route::prefix("mini")->group(function () {
    Route::post("login", [Mini\AuthController::class, "login"]);

    //注册账号
    Route::post("register", [Mini\AuthController::class, "register"]);
    Route::post("register_first_step_check", [Mini\AuthController::class, "registerFirstStepCheck"]);
    Route::post('reset_password', [Mini\AuthController::class, "resetPassword"]);
    Route::get("invitation_code/{code}", [Mini\AuthController::class, "invitationCode"]);

    /**
     * 获取验证码
     */
    Route::post('send_code', [Common\PhoneCodeController::class, 'send']);

    Route::post('get_captcha', [Common\CaptchaController::class, 'getCaptcha']);
    Route::get('departments', [Mini\RegionController::class, "departments"]);
    Route::get('department_tree', [Mini\RegionController::class, "tree"]);

    Route::get('business_types', [Mini\HomeController::class, "businessTypes"]);

    Route::get('index', [Mini\HomeController::class, "index"]);
    Route::get('articles', [Mini\HomeController::class, "articles"]);
    Route::get('article/{id}', [Mini\HomeController::class, "article"]);
    Route::get('page/{id}', [Mini\HomeController::class, "page"]);
    Route::get('categories', [Mini\HomeController::class, "categories"]);
    Route::get('category/{id}', [Mini\HomeController::class, "category"]);
    Route::get('config', [Common\ConfigController::class, "get"]);
    Route::get('stage', [Mini\HomeController::class, "stage"]);
    Route::middleware(['auth:api', 'permissions'])->group(function () {

        // ==========================================校验人脸验证和激活状态===================================================================
        Route::middleware(['activatedCheck', 'faceCheck'])->group(function () {
            Route::post('upload_file', [Common\FileController::class, 'uploadFile']);

            Route::get("info", [Mini\AuthController::class, "info"])->withoutMiddleware(['activatedCheck', 'faceCheck']);
            Route::post("info", [Mini\AuthController::class, "info"])->withoutMiddleware(['activatedCheck', 'faceCheck']);
            Route::post('update_password', [Mini\AuthController::class, "updatePassword"]);

            /**
             * 学习答题相关
             */
            Route::get("course", [Mini\CourseController::class, "list"]);
            Route::get("chapter", [Mini\ChapterController::class, "list"]);
            Route::get('chapter/{id}', [Mini\ChapterController::class, 'detail']);
            Route::post('upload_course_process', [Mini\CourseController::class, 'uploadCourseProcess']);

            /**
             * 练习测试
             */
            Route::get('exam_paper', [Mini\PaperController::class, 'examPaper']);

            Route::get('paper/{id}', [Mini\PaperController::class, 'detail']);
            Route::post('paper/{id}/start', [Mini\PaperController::class, 'start']);
            Route::post('paper/{round_id}/next_question', [Mini\PaperController::class, 'nextQuestion']);
            Route::post('paper/{round_id}/pre_question', [Mini\PaperController::class, 'preQuestion']);
            Route::post('paper/{round_id}/answer', [Mini\PaperController::class, 'answer']);
            Route::post('paper/{round_id}/finish', [Mini\PaperController::class, 'finish']);
            Route::get('paper/{round_id}/questions', [Mini\PaperController::class, 'questions']);

            /**
             * 错题本刷题
             */
            Route::get('error_question', [Mini\ErrorQuestionController::class, 'list']);
            Route::delete('error_question/{id}', [Mini\ErrorQuestionController::class, 'destroy']);
            Route::post('error_question_start', [Mini\ErrorQuestionController::class, 'start']);
            Route::post('error_question/{round}/next_question', [Mini\ErrorQuestionController::class, 'nextQuestion']);
            Route::post('error_question/{round}/pre_question', [Mini\ErrorQuestionController::class, 'preQuestion']);
            Route::post('error_question/{round}/answer', [Mini\ErrorQuestionController::class, 'answer']);
            Route::post('error_question/{round}/finish', [Mini\ErrorQuestionController::class, 'finish']);


            /**
             * 题目
             */
            Route::get('question/{id}', [Mini\QuestionController::class, 'show']);

            /**
             * 证书
             */
            Route::get('certificate', [Mini\AuthController::class, 'certificate'])->withoutMiddleware(['activatedCheck', 'faceCheck']);
            Route::get('certificate/{id}', [Mini\AuthController::class, 'certificateDetail']);

            /**
             * 咨询
             */
            Route::any('consult_article', [Mini\ConsultController::class, 'article']);

            /**
             * 收藏
             */
            Route::post('collect', [Mini\HomeController::class, 'collect']);
            Route::get('collect', [Mini\HomeController::class, 'myCollect']);

            /**
             * 举报
             */
            Route::post('report', [Mini\ReportController::class, 'store']);
            Route::get('report', [Mini\ReportController::class, 'list']);
            Route::get('report/{id}', [Mini\ReportController::class, 'show']);
            Route::post('report/{id}/reply', [Mini\ReportController::class, 'reply']);
            Route::get('report_statistics', [Mini\ReportController::class, 'statistics']);

            /**
             * 任务
             */
            Route::get('work_task_type', [Mini\WorkTaskController::class, 'workTaskType']);
            Route::resource('work_task', Mini\WorkTaskController::class);
            Route::resource('work_task_report', Mini\WorkTaskReportController::class);

            /**
             * 通知
             */
            Route::resource('inform', Mini\InformController::class)->withoutMiddleware(['activatedCheck', 'faceCheck']);

            /**
             * 积分
             */
            Route::get('point', [Mini\AuthController::class, 'point']);
            Route::get('point_rank', [Mini\AuthController::class, 'pointRank']);


            // 特殊时期征召
            Route::get('recall_task', [Mini\RecallController::class, 'recallTask']);
            Route::get('recall_task/{id}', [Mini\RecallController::class, 'recallTaskDetail']);
            Route::get('recall_task_soldier', [Mini\RecallController::class, 'recallTaskSoldier']);
            Route::get('recall_task_soldier/{id}', [Mini\RecallController::class, 'recallTaskSoldierDetail']);
            Route::post('recall_task_soldier_stage_finish/{id}', [Mini\RecallController::class, 'recallTaskSoldierStageFinish']);

            Route::post('call', [Mini\AuthController::class, 'call']);

             // 体检计划
             Route::get('mission_physical_examination', [Mini\MissionPhysicalExaminationController::class, 'list']);
             // 体检人员列表
             Route::get('mission_physical_examination_person_list', [Mini\MissionPhysicalExaminationController::class, 'personList']);
 

            // 统计
            // 学习统计
            Route::get('statistics/course', [Mini\StatisticsController::class, 'courseStatistics']);
            // 考核统计
            Route::get('statistics/paper', [Mini\StatisticsController::class, 'paperStatistics']);
            // 考核统计考试成绩
            Route::get('statistics/paper_result', [Mini\StatisticsController::class, 'paperResultStatistics']);
            // 工作任务
            Route::get('statistics/work_task', [Mini\StatisticsController::class, 'workTaskStatistics']);
            // 征招
            Route::get('statistics/recall_task', [Mini\StatisticsController::class, 'recallTaskStatistics']);
            // 回访
            Route::get('statistics/out_notice_task', [Mini\StatisticsController::class, 'outNoticeTaskStatistics']);
            // 学校人员统计
            Route::get('statistics/mission_school_person_list', [Mini\StatisticsController::class, 'missionSchoolPersonList']);
            // 社会人员统计
            Route::get('statistics/mission_social_person_list', [Mini\StatisticsController::class, 'missionSocialPersonList']);
            // 意向统计
            Route::get('statistics/mission_intention_list', [Mini\StatisticsController::class, 'missionIntentionList']);
            // 宣传比对统计
            Route::get('statistics/mission_promote_compare_list', [Mini\StatisticsController::class, 'missionPromoteCompareList']);
            // 政考统计
            Route::get('statistics/mission_political_exam_list', [Mini\StatisticsController::class, 'missionPoliticalExamList']);
            // 教育统计
            Route::get('statistics/mission_education_list', [Mini\StatisticsController::class, 'missionEducationList']);
            // 完成数统计
            Route::get('statistics/mission_go_list', [Mini\StatisticsController::class, 'missionGoList']);
            // 体检统计
            Route::get('statistics/mission_physical_check_list', [Mini\StatisticsController::class, 'missionPhysicalCheckList']);
            // 体检复查统计
            Route::get('statistics/mission_physical_recheck_list', [Mini\StatisticsController::class, 'missionPhysicalRecheckList']);
            // 体检抽查统计
            Route::get('statistics/mission_physical_spot_check_list', [Mini\StatisticsController::class, 'missionPhysicalSpotCheckList']);
            // 任务数统计
            Route::get('statistics/mission_task_num_list', [Mini\StatisticsController::class, 'missionTaskNumList']);
           
            /***
             * 小红点
             */
            Route::get('statistics/remind_count', [Mini\StatisticsController::class, 'remindCount'])->withoutMiddleware(['activatedCheck', 'faceCheck']);

            // 通知
            Route::get("notification", [Common\NotificationController::class, 'notification']);
            Route::get("notification/{id}", [Common\NotificationController::class, 'notificationDetail']);
            Route::post("notification_read/{id}", [Common\NotificationController::class, 'notificationRead']);


            /**
             * 任务
             */
            Route::resource('task', Admin\Task\TaskController::class)->only([
                'index',
                'store',
            ]);

            /**
             * 平时任务
             */
            Route::resource('mission', Mini\MissionController::class)->only([
                'index',
                'show',
            ]);

            /**
             * 平时任务人员 面包屑
             */
            Route::resource('mission_person', Mini\MissionPersonController::class)->only([
                'index',
                'show',
                'update'
            ]);
        });

        // ==========================================不校验人脸验证和激活状态===================================================================
        // 人脸验证
        Route::get('get_user_id_key', [Mini\AuthController::class, 'getUserIdKey']);
        Route::get('get_user_face_result', [Mini\AuthController::class, 'getUserFaceResult']);

        // 账号激活
        Route::post('account_activate', [Mini\AuthController::class, 'accountActivate']);
    });

    // 特殊时期征招
    Route::get('recall_get_user_id_key', [Mini\RecallController::class, 'faceGetUserIdKey']);
    Route::get('recall_get_user_face_result', [Mini\RecallController::class, 'faceGetUserFaceResult']);
    Route::get('recall_get_user_info', [Mini\RecallController::class, 'faceGetUserInfo']);

    /**
     * 调查
     */
    Route::get('survey', [Mini\SurveyController::class, 'list']);
    Route::get('survey/{id}', [Mini\SurveyController::class, 'detail']);
    Route::post('survey/{id}/submit', [Mini\SurveyController::class, 'answer']);

});

/**
 * 获取access_token，用于开发环境调试
 */
Route::get('get_access_token_zSDsXgfP2QH4MRM', [Common\AccessTokenController::class, 'getAccessToken']);
