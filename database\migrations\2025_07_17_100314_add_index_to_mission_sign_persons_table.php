<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mission_sign_persons', function (Blueprint $table) {
            $table->index("district_code", "district_code");
            $table->index(["mission_id", "district_code", "id_card"], "mission_id_district_code_id_card");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mission_sign_persons', function (Blueprint $table) {
            $table->dropIndex("district_code");
            $table->dropIndex("mission_id_district_code_id_card");
        });
    }
};
