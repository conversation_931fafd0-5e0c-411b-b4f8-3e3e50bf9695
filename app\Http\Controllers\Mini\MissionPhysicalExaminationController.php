<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\MissionPhysicalExaminationService;
use Illuminate\Http\Request;

class MissionPhysicalExaminationController
{
    /**
     * 获取体检计划列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        $params = $request->all();
        $service = new MissionPhysicalExaminationService();
        $resp = $service->list($params);
        
        if ($service->hasError()) {
            return error($service->getError());
        }
        
        return success($resp);
    }

    /**
     * 获取体检人员列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function personList(Request $request)
    {
        $params = $request->all();
        $service = new MissionPhysicalExaminationService();
        $resp = $service->personList($params);
        
        if ($service->hasError()) {
            return error($service->getError());
        }
        
        return success($resp);
    }
} 